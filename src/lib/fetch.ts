/* eslint-disable @typescript-eslint/no-explicit-any */
import qs from 'qs';
import { showFailToast } from 'vant';
import config from '@/config';
import ssoWeb from '@/lib/sso';

class FetchSingleton {
  private static instance: FetchSingleton;

  public static accessToken: string;

  public static getInstance(): FetchSingleton {
    if (!FetchSingleton.instance) {
      FetchSingleton.instance = new FetchSingleton();
    }
    return FetchSingleton.instance;
  }

  public static getAccessToken(): string {
    return FetchSingleton.accessToken;
  }

  async fetch(
    path: string,
    options: RequestInit & {
      headers?: HeadersInit;
      params?: { [prop: string]: any };
      timeout?: number;
      responseType?: 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream';
    } = {},
  ): Promise<any> {
    const { headers, timeout = 10000, responseType = 'json', params, ...restOptions } = options;
    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      'x-requested-with': 'XMLHttpRequest',
      'access-token': FetchSingleton.accessToken,
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    let url = path;
    if (params) {
      if (Object.prototype.toString.call(params) === '[object Object]') {
        const paramsStr = qs.stringify(params);
        url += `${url.includes('?') ? '&' : '?'}${paramsStr}`;
      }
    }
    const isWeatherAPI = path.includes('/humanrelation/get_weather');

    try {
      const response = await fetch(url, {
        headers: {
          ...defaultHeaders,
          ...headers,
        },
        signal: controller.signal,
        ...restOptions,
      });

      if (!response.ok) {
        throw new Error(`Fetch error: ${response.statusText}`);
      }
      // sse流式数据请求返回值处理
      if (responseType !== 'json') {
        return response;
      }
      const res = await response.json();
      res.status = res.status === undefined ? res.code : res.status;
      if (res?.status === 401) {
        // 401 未登录
        // eslint-disable-next-line no-return-assign
        return (window.location.href = ssoWeb.getLoginUrl());
      }
      if (res?.status === 403) {
        // 403 无权限
        showFailToast('您暂无该页面内容权限，即将为您跳转回首页');
        const timer = setTimeout(() => {
          clearTimeout(timer);
          window.location.href = `${window.origin}${config.baseRoute}`;
        }, 3000);
        return;
      }
      // 检查是否为成功响应
      // 1. 对于有result字段的API（如关系图API），检查result === 'success'
      // 2. 对于有status字段的API，检查status === 200 或 0
      // 3. 对于有errcode字段的API，检查errcode === 0
      // 4. 对于天气API，result === 'error' 也是正常的业务响应
      const isSuccess =
        res.result === 'success' ||
        res.status === 200 ||
        res.status === 0 ||
        res.errcode === 0 ||
        (res.status === undefined && res.errcode === undefined && res.result === undefined) ||
        (isWeatherAPI && res.result === 'error'); // 天气API的错误响应也是成功的HTTP请求

      if (!isSuccess) {
        if ((res?.message as string) !== '用户档案不存在') {
          showFailToast((res?.reason as string) || (res?.message as string) || '接口错误');
        }
        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject({
          status: res?.status,
          message: res?.message,
        });
      }

      return res;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      if (error.name === 'AbortError') {
        throw new Error('Fetch timeout');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  setAccessToken(ssoid: string) {
    FetchSingleton.accessToken = ssoid;
  }
}

export default FetchSingleton.getInstance();

export const { getAccessToken } = FetchSingleton;
